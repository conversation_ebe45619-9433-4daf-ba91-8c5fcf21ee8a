package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import spock.lang.Specification

/**
 * TextFunction 单元测试
 * 测试 STARTWITH, ENDWITH, EQUALS, LEN, CONTAINS, INCLUDES, ISSelectOptionVAL, SPLIT, TRIM, MATCH, NUMBERSTRINGRMB, NUMBERSTRING, VALUE, NULL2EMPTY 等方法的 compile 和 evaluate 场景
 */
class TextFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test text functions compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'STARTWITH("hello", "he")',
                'ENDWITH("hello", "lo")',
                'EQUALS("test", "test")',
                'LEN("hello")',
                'CONTAINS("hello", "ell")',
                'CONTAINS(["a", "b"], "a")',
                'INCLUDES(["a", "b"] as String[], ["a", "b", "c"] as String[])',
                'ISSelectOptionVAL("a", ["a", "b", "c"] as String[])',
                'SPLIT("a,b,c", ",", 1)',
                'TRIM("  hello  ")',
                'MATCH("123", "[0-9]+")',
                'NUMBERSTRING(123)',
                'NUMBERSTRINGRMB(123.45)',
                'VALUE("123.45")',
                'NULL2EMPTY(null)'
        ]
    }

    def "test STARTWITH evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'STARTWITH("hello", "he")'      | [:]                                   || true
        'STARTWITH("hello", "hi")'      | [:]                                   || false
        'STARTWITH("hello", "")'        | [:]                                   || true
        'STARTWITH("", "he")'           | [:]                                   || false
        'STARTWITH(null, "he")'         | [:]                                   || false
        'STARTWITH(text, prefix)'       | [text: "hello", prefix: "he"]         || true
        'STARTWITH(text, prefix)'       | [text: "hello", prefix: "hi"]         || false
        'STARTWITH(text, prefix)'       | [text: null, prefix: "he"]            || false
    }

    def "test ENDWITH evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'ENDWITH("hello", "lo")'        | [:]                                   || true
        'ENDWITH("hello", "la")'        | [:]                                   || false
        'ENDWITH("hello", "")'          | [:]                                   || true
        'ENDWITH("", "lo")'             | [:]                                   || false
        'ENDWITH(null, "lo")'           | [:]                                   || false
        'ENDWITH(text, suffix)'         | [text: "hello", suffix: "lo"]         || true
        'ENDWITH(text, suffix)'         | [text: "hello", suffix: "la"]         || false
        'ENDWITH(text, suffix)'         | [text: null, suffix: "lo"]            || false
    }

    def "test EQUALS evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'EQUALS("test", "test")'        | [:]                                   || true
        'EQUALS("test", "Test")'        | [:]                                   || false
        'EQUALS("test", "other")'       | [:]                                   || false
        'EQUALS("", "")'                | [:]                                   || true
        'EQUALS(null, "test")'          | [:]                                   || false
        'EQUALS(text1, text2)'          | [text1: "hello", text2: "hello"]      || true
        'EQUALS(text1, text2)'          | [text1: "hello", text2: "world"]      || false
        'EQUALS(text1, text2)'          | [text1: null, text2: "hello"]         || false
    }

    def "test LEN evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression              | bindings                          || expectedResult
        'LEN("hello")'          | [:]                               || 5L
        'LEN("")'               | [:]                               || 0L
        'LEN("中文")'           | [:]                               || 2L
        'LEN(null)'             | [:]                               || 0L
        'LEN(text)'             | [text: "hello world"]             || 11L
        'LEN(text)'             | [text: ""]                        || 0L
        'LEN(text)'             | [text: null]                      || 0L
    }

    def "test CONTAINS evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                          | bindings                              || expectedResult
        'CONTAINS("hello", "ell")'          | [:]                                   || true
        'CONTAINS("hello", "xyz")'          | [:]                                   || false
        'CONTAINS("hello", "")'             | [:]                                   || true
        'CONTAINS("", "ell")'               | [:]                                   || false
        'CONTAINS(null, "ell")'             | [:]                                   || false
        'CONTAINS(["a", "b"], "a")'         | [:]                                   || true
        'CONTAINS(["a", "b"], "c")'         | [:]                                   || false
        'CONTAINS(null, "a")'               | [:]                                   || false
        'CONTAINS(text, search)'            | [text: "hello world", search: "world"] || true
        'CONTAINS(text, search)'            | [text: "hello world", search: "xyz"]   || false
    }

    def "test INCLUDES evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                      | bindings                              || expectedResult
        'INCLUDES(["a", "b"] as String[], ["a", "b", "c"] as String[])' | [:]                                   || true
        'INCLUDES(["a", "d"] as String[], ["a", "b", "c"] as String[])' | [:]                                   || false
        'INCLUDES(["a"] as String[], ["a", "b", "c"] as String[])'      | [:]                                   || true
        'INCLUDES([] as String[], ["a", "b", "c"] as String[])'         | [:]                                   || false
        'INCLUDES(null, ["a", "b", "c"] as String[])'                   | [:]                                   || false
        'INCLUDES(["a"] as String[], null)'                             | [:]                                   || false
    }

    def "test ISSelectOptionVAL evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                              || expectedResult
        'ISSelectOptionVAL("a", ["a", "b", "c"] as String[])'   | [:]                                   || true
        'ISSelectOptionVAL("d", ["a", "b", "c"] as String[])'   | [:]                                   || false
        'ISSelectOptionVAL("", ["a", "b", "c"] as String[])'    | [:]                                   || false
        'ISSelectOptionVAL(null, ["a", "b", "c"] as String[])'  | [:]                                   || false
        'ISSelectOptionVAL("a", null)'                          | [:]                                   || false
        'ISSelectOptionVAL(option, options)'                    | [option: "b", options: ["a", "b", "c"] as String[]] || true
        'ISSelectOptionVAL(option, options)'                    | [option: "d", options: ["a", "b", "c"] as String[]] || false
    }

    def "test SPLIT evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                          | bindings                              || expectedResult
        'SPLIT("a,b,c", ",", 0)'            | [:]                                   || "a"
        'SPLIT("a,b,c", ",", 1)'            | [:]                                   || "b"
        'SPLIT("a,b,c", ",", 2)'            | [:]                                   || "c"
        'SPLIT("a,b,c", ",", 3)'            | [:]                                   || null
        'SPLIT("a,b,c", ",", -1)'           | [:]                                   || null
        'SPLIT("hello world", " ", 0)'      | [:]                                   || "hello"
        'SPLIT("hello world", " ", 1)'      | [:]                                   || "world"
        'SPLIT("x|y|z", "\\\\|", 0)'        | [:]                                   || "x"
        'SPLIT("x|y|z", "\\\\|", 1)'        | [:]                                   || "y"
        'SPLIT("x|y|z", "\\\\|", 2)'        | [:]                                   || "z"
        'SPLIT(null, ",", 0)'               | [:]                                   || null
        'SPLIT("a,b,c", null, 0)'           | [:]                                   || null
        'SPLIT(text, delimiter, index)'     | [text: "x,y,z", delimiter: ",", index: 1] || "y"
    }

    def "test TRIM evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                  | bindings                          || expectedResult
        'TRIM("  hello  ")'         | [:]                               || "hello"
        'TRIM("hello")'             | [:]                               || "hello"
        'TRIM("")'                  | [:]                               || ""
        'TRIM("   ")'               | [:]                               || ""
        'TRIM(null)'                | [:]                               || null
        'TRIM("\\thello\\n")'       | [:]                               || "hello"
        'TRIM(text)'                | [text: "  world  "]               || "world"
        'TRIM(text)'                | [text: null]                      || null
    }

    def "test MATCH evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'MATCH("123", "[0-9]+")'        | [:]                                   || true
        'MATCH("abc", "[0-9]+")'        | [:]                                   || false
        'MATCH("hello", "h.*o")'        | [:]                                   || true
        'MATCH("hello", "H.*o")'        | [:]                                   || false
        'MATCH("", ".*")'               | [:]                                   || true
        'MATCH(null, ".*")'             | [:]                                   || false
        'MATCH("13812345678", "[0-9]{11}")' | [:]                               || true
        'MATCH("1381234567", "[0-9]{11}")' | [:]                                || false
        'MATCH(text, pattern)'          | [text: "123", pattern: "[0-9]+"]      || true
        'MATCH(text, pattern)'          | [text: "abc", pattern: "[0-9]+"]      || false
    }

    def "test NUMBERSTRING evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                              | bindings                              || expectedResult
        'NUMBERSTRING(0)'                       | [:]                                   || "零"
        'NUMBERSTRING(123)'                     | [:]                                   || "壹佰贰拾叁"
        'NUMBERSTRING(1000)'                    | [:]                                   || "壹仟"
        'NUMBERSTRING(10000)'                   | [:]                                   || "壹万"
        'NUMBERSTRING(-123)'                    | [:]                                   || "负壹佰贰拾叁"
        'NUMBERSTRING(123.45)'                  | [:]                                   || "壹佰贰拾叁点肆伍"
        'NUMBERSTRING(null)'                    | [:]                                   || null
        'NUMBERSTRING(number)'                  | [number: 456]                         || "肆佰伍拾陆"
        'NUMBERSTRING(decimal)'                 | [decimal: new BigDecimal("123.45")]   || "壹佰贰拾叁点肆伍"
    }

    def "test NUMBERSTRINGRMB evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                              | bindings                              || expectedResult
        'NUMBERSTRINGRMB(0)'                    | [:]                                   || "零元整"
        'NUMBERSTRINGRMB(123)'                  | [:]                                   || "壹佰贰拾叁元整"
        'NUMBERSTRINGRMB(123.45)'               | [:]                                   || "壹佰贰拾叁元肆角伍分"
        'NUMBERSTRINGRMB(123.40)'               | [:]                                   || "壹佰贰拾叁元肆角"
        'NUMBERSTRINGRMB(123.05)'               | [:]                                   || "壹佰贰拾叁元零伍分"
        'NUMBERSTRINGRMB(-123.45)'              | [:]                                   || "负壹佰贰拾叁元肆角伍分"
        'NUMBERSTRINGRMB(null)'                 | [:]                                   || null
        'NUMBERSTRINGRMB(amount)'               | [amount: 456.78]                      || "肆佰伍拾陆元柒角捌分"
        'NUMBERSTRINGRMB(decimal)'              | [decimal: new BigDecimal("123.45")]   || "壹佰贰拾叁元肆角伍分"
    }

    def "test VALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression              | bindings                          || expectedResult
        'VALUE("123")'          | [:]                               || new BigDecimal("123")
        'VALUE("123.45")'       | [:]                               || new BigDecimal("123.45")
        'VALUE("-123.45")'      | [:]                               || new BigDecimal("-123.45")
        'VALUE("0")'            | [:]                               || new BigDecimal("0")
        'VALUE("abc")'          | [:]                               || null
        'VALUE("")'             | [:]                               || null
        'VALUE(null)'           | [:]                               || null
        'VALUE(text)'           | [text: "456.78"]                  || new BigDecimal("456.78")
        'VALUE(text)'           | [text: "invalid"]                 || null
    }

    def "test NULL2EMPTY evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression              | bindings                          || expectedResult
        'NULL2EMPTY(null)'      | [:]                               || ""
        'NULL2EMPTY("test")'    | [:]                               || "test"
        'NULL2EMPTY("")'        | [:]                               || ""
        'NULL2EMPTY(123)'       | [:]                               || "123"
        'NULL2EMPTY(true)'      | [:]                               || "true"
        'NULL2EMPTY(value)'     | [value: null]                     || ""
        'NULL2EMPTY(value)'     | [value: "hello"]                  || "hello"
        'NULL2EMPTY(value)'     | [value: 456]                      || "456"
    }

    def "test complex text expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                  | bindings                                      || expectedResult
        'LEN(TRIM(text))'                                           | [text: "  hello  "]                           || 5L
        'CONTAINS(TRIM(text), "world")'                             | [text: "  hello world  "]                     || true
        'STARTWITH(TRIM(text), "hello")'                            | [text: "  hello world"]                       || true
        'ENDWITH(TRIM(text), "world")'                              | [text: "hello world  "]                       || true
        'VALUE(SPLIT(text, ",", 1))'                                | [text: "100,200,300"]                         || new BigDecimal("200")
        'LEN(NULL2EMPTY(name))'                                     | [name: null]                                  || 0L
        'LEN(NULL2EMPTY(name))'                                     | [name: "John"]                                || 4L
        'EQUALS(TRIM(input), expected)'                             | [input: "  test  ", expected: "test"]         || true
        'MATCH(TRIM(phone), "[0-9]{11}")'                           | [phone: "  13812345678  "]                    || true
        'MATCH(TRIM(phone), "[0-9]{11}")'                           | [phone: "  1381234567  "]                     || false
        'NUMBERSTRINGRMB(VALUE(amount))'                            | [amount: "123.45"]                            || "壹佰贰拾叁元肆角伍分"
        'NUMBERSTRING(VALUE(amount))'                               | [amount: "123.45"]                            || "壹佰贰拾叁点肆伍"
        'STARTWITH(NUMBERSTRING(num), "壹")'                        | [num: 123]                                    || true
        'ISSelectOptionVAL(TRIM(option), options)'                  | [option: "  a  ", options: ["a", "b"] as String[]] || true
    }

    def "test Chinese and Unicode text processing"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Chinese character tests
        'LEN("中文测试")'                                        | [:]                                           || 4L
        'LEN("Hello世界")'                                       | [:]                                           || 7L
        'STARTWITH("中文测试", "中文")'                           | [:]                                           || true
        'STARTWITH("中文测试", "测试")'                           | [:]                                           || false
        'ENDWITH("中文测试", "测试")'                             | [:]                                           || true
        'ENDWITH("中文测试", "中文")'                             | [:]                                           || false
        'CONTAINS("中文测试字符串", "测试")'                       | [:]                                           || true
        'CONTAINS("中文测试字符串", "英文")'                       | [:]                                           || false
        'EQUALS("中文", "中文")'                                 | [:]                                           || true
        'EQUALS("中文", "英文")'                                 | [:]                                           || false
        // Mixed Chinese and English
        'LEN("Hello中文World世界")'                              | [:]                                           || 13L
        'STARTWITH("Hello中文", "Hello")'                        | [:]                                           || true
        'ENDWITH("Hello中文", "中文")'                           | [:]                                           || true
        'CONTAINS("Hello中文World", "中文")'                     | [:]                                           || true
        // Unicode special characters
        'LEN("🌟⭐✨")'                                          | [:]                                           || 3L
        'CONTAINS("Hello🌟World", "🌟")'                        | [:]                                           || true
        'STARTWITH("🌟Hello", "🌟")'                            | [:]                                           || true
        'ENDWITH("Hello🌟", "🌟")'                              | [:]                                           || true
    }

    def "test special characters and edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Empty and whitespace strings
        'LEN("")'                                               | [:]                                           || 0L
        'LEN(" ")'                                              | [:]                                           || 1L
        'LEN("   ")'                                            | [:]                                           || 3L
        'STARTWITH("", "")'                                     | [:]                                           || true
        'ENDWITH("", "")'                                       | [:]                                           || true
        'CONTAINS("", "")'                                      | [:]                                           || true
        'EQUALS("", "")'                                        | [:]                                           || true
        // Special characters
        'LEN("\\n\\t\\r")'                                      | [:]                                           || 6L
        'CONTAINS("Hello\\nWorld", "\\n")'                      | [:]                                           || true
        'STARTWITH("\\tHello", "\\t")'                          | [:]                                           || true
        'ENDWITH("Hello\\r", "\\r")'                            | [:]                                           || true
        // Quotes and escape characters
        'LEN("\\"Hello\\"World\\"")'                            | [:]                                           || 13L
        'CONTAINS("Say \\"Hello\\"", "\\"Hello\\"")'            | [:]                                           || true
        'STARTWITH("\\"Hello\\"", "\\"")'                       | [:]                                           || true
        'ENDWITH("\\"Hello\\"", "\\"")'                         | [:]                                           || true
        // Numbers as strings
        'LEN("12345")'                                          | [:]                                           || 5L
        'STARTWITH("12345", "123")'                             | [:]                                           || true
        'ENDWITH("12345", "345")'                               | [:]                                           || true
        'CONTAINS("12345", "234")'                              | [:]                                           || true
        'EQUALS("123", "123")'                                  | [:]                                           || true
    }

    def "test TRIM function edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Various whitespace characters
        'TRIM("   hello   ")'                                   | [:]                                           || "hello"
        'TRIM("\\thello\\t")'                                   | [:]                                           || "hello"
        'TRIM("\\nhello\\n")'                                   | [:]                                           || "hello"
        'TRIM("\\rhello\\r")'                                   | [:]                                           || "hello"
        'TRIM("   \\t\\n\\rhello\\r\\n\\t   ")'                 | [:]                                           || "hello"
        // Only whitespace
        'TRIM("   ")'                                           | [:]                                           || ""
        'TRIM("\\t\\n\\r")'                                     | [:]                                           || ""
        'TRIM("")'                                              | [:]                                           || ""
        // No trimming needed
        'TRIM("hello")'                                         | [:]                                           || "hello"
        'TRIM("hello world")'                                   | [:]                                           || "hello world"
        // Chinese characters with whitespace
        'TRIM("   中文测试   ")'                                 | [:]                                           || "中文测试"
        'TRIM("\\t中文\\n")'                                    | [:]                                           || "中文"
    }

    def "test SPLIT function comprehensive cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Different delimiters
        'SPLIT("a,b,c", ",", 0)'                                | [:]                                           || "a"
        'SPLIT("a,b,c", ",", 1)'                                | [:]                                           || "b"
        'SPLIT("a,b,c", ",", 2)'                                | [:]                                           || "c"
        'SPLIT("a;b;c", ";", 0)'                                | [:]                                           || "a"
        'SPLIT("a|b|c", "|", 1)'                                | [:]                                           || "b"
        'SPLIT("a b c", " ", 2)'                                | [:]                                           || "c"
        // Edge cases
        'SPLIT("", ",", 0)'                                     | [:]                                           || ""
        'SPLIT("a", ",", 0)'                                    | [:]                                           || "a"
        'SPLIT("a,", ",", 0)'                                   | [:]                                           || "a"
        'SPLIT("a,", ",", 1)'                                   | [:]                                           || ""
        'SPLIT(",a", ",", 0)'                                   | [:]                                           || ""
        'SPLIT(",a", ",", 1)'                                   | [:]                                           || "a"
        // Chinese text splitting
        'SPLIT("中文,测试,字符串", ",", 0)'                       | [:]                                           || "中文"
        'SPLIT("中文,测试,字符串", ",", 1)'                       | [:]                                           || "测试"
        'SPLIT("中文,测试,字符串", ",", 2)'                       | [:]                                           || "字符串"
        // Multiple character delimiters
        'SPLIT("a::b::c", "::", 0)'                             | [:]                                           || "a"
        'SPLIT("a::b::c", "::", 1)'                             | [:]                                           || "b"
        'SPLIT("a::b::c", "::", 2)'                             | [:]                                           || "c"
    }

    def "test NUMBERSTRING and NUMBERSTRINGRMB edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Zero and small numbers
        'NUMBERSTRING(0)'                                       | [:]                                           || "零"
        'NUMBERSTRING(1)'                                       | [:]                                           || "壹"
        'NUMBERSTRING(10)'                                      | [:]                                           || "壹拾"
        'NUMBERSTRING(11)'                                      | [:]                                           || "壹拾壹"
        'NUMBERSTRING(100)'                                     | [:]                                           || "壹佰"
        'NUMBERSTRING(101)'                                     | [:]                                           || "壹佰零壹"
        'NUMBERSTRING(110)'                                     | [:]                                           || "壹佰壹拾"
        'NUMBERSTRING(111)'                                     | [:]                                           || "壹佰壹拾壹"
        // Large numbers
        'NUMBERSTRING(1000)'                                    | [:]                                           || "壹仟"
        'NUMBERSTRING(10000)'                                   | [:]                                           || "壹万"
        'NUMBERSTRING(100000000)'                               | [:]                                           || "壹亿"
        // Decimal numbers
        'NUMBERSTRING(1.5)'                                     | [:]                                           || "壹点伍"
        'NUMBERSTRING(12.34)'                                   | [:]                                           || "壹拾贰点叁肆"
        'NUMBERSTRING(100.01)'                                  | [:]                                           || "壹佰点零壹"
        // RMB format
        'NUMBERSTRINGRMB(0)'                                    | [:]                                           || "零元整"
        'NUMBERSTRINGRMB(1)'                                    | [:]                                           || "壹元整"
        'NUMBERSTRINGRMB(1.5)'                                  | [:]                                           || "壹元伍角"
        'NUMBERSTRINGRMB(1.05)'                                 | [:]                                           || "壹元零伍分"
        'NUMBERSTRINGRMB(12.34)'                                | [:]                                           || "壹拾贰元叁角肆分"
        'NUMBERSTRINGRMB(100.00)'                               | [:]                                           || "壹佰元整"
    }

    def "test performance with large strings"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Large string operations
        'LEN(largeText)'                                        | [largeText: "A" * 1000]                      || 1000L
        'STARTWITH(largeText, "AAA")'                           | [largeText: "A" * 1000]                      || true
        'ENDWITH(largeText, "AAA")'                             | [largeText: "A" * 1000]                      || true
        'CONTAINS(largeText, "AAA")'                            | [largeText: "A" * 1000]                      || true
        // Large Chinese text
        'LEN(chineseText)'                                      | [chineseText: "中" * 500]                     || 500L
        'STARTWITH(chineseText, "中中中")'                       | [chineseText: "中" * 500]                     || true
        'ENDWITH(chineseText, "中中中")'                         | [chineseText: "中" * 500]                     || true
        'CONTAINS(chineseText, "中中中")'                        | [chineseText: "中" * 500]                     || true
        // Mixed large text
        'LEN(mixedText)'                                        | [mixedText: "A中" * 250]                      || 500L
        'STARTWITH(mixedText, "A中A")'                          | [mixedText: "A中" * 250]                      || true
        'CONTAINS(mixedText, "A中A")'                           | [mixedText: "A中" * 250]                      || true
    }
}
