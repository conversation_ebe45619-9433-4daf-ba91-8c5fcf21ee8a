package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import spock.lang.Specification

/**
 * LogicFunction 单元测试
 * 测试 NOT, AND, OR, IF, ISNULL, NOTNULL, ISBLANK, NOTBLANK, ISNUMBER, NULLVALUE, NULL2DEFAULT 等方法的 compile 和 evaluate 场景
 */
class LogicFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test logic functions compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'NOT(true)',
                'NOT(false)',
                'AND(true, true)',
                'AND(true, false, true)',
                'OR(false, false)',
                'OR(true, false, false)',
                'IF(true, "yes", "no")',
                'IF(false, 1, 2)',
                'ISNULL(null)',
                'ISNULL("test")',
                'NOTNULL("test")',
                'NOTNULL(null)',
                'ISBLANK("")',
                'ISBLANK("test")',
                'NOTBLANK("test")',
                'NOTBLANK("")',
                'ISNUMBER("123")',
                'ISNUMBER("abc")',
                'NULLVALUE(null, "default")',
                'NULLVALUE("value", "default")',
                'NULL2DEFAULT(null, "default")',
                'NULL2DEFAULT("value", "default")'
        ]
    }

    def "test NOT evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression          | bindings                  || expectedResult
        'NOT(true)'         | [:]                       || false
        'NOT(false)'        | [:]                       || true
        'NOT(value)'        | [value: true]             || false
        'NOT(value)'        | [value: false]            || true
    }

    def "test AND evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'AND(true, true)'               | [:]                                   || true
        'AND(true, false)'              | [:]                                   || false
        'AND(false, true)'              | [:]                                   || false
        'AND(false, false)'             | [:]                                   || false
        'AND(true, true, true)'         | [:]                                   || true
        'AND(true, true, false)'        | [:]                                   || false
        'AND(a, b, c)'                  | [a: true, b: true, c: true]           || true
        'AND(a, b, c)'                  | [a: true, b: false, c: true]          || false
    }

    def "test OR evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'OR(true, true)'                | [:]                                   || true
        'OR(true, false)'               | [:]                                   || true
        'OR(false, true)'               | [:]                                   || true
        'OR(false, false)'              | [:]                                   || false
        'OR(false, false, true)'        | [:]                                   || true
        'OR(false, false, false)'       | [:]                                   || false
        'OR(a, b, c)'                   | [a: false, b: false, c: true]         || true
        'OR(a, b, c)'                   | [a: false, b: false, c: false]        || false
    }

    def "test IF evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'IF(true, "yes", "no")'         | [:]                                   || "yes"
        'IF(false, "yes", "no")'        | [:]                                   || "no"
        'IF(true, 1, 2)'                | [:]                                   || 1
        'IF(false, 1, 2)'               | [:]                                   || 2
        'IF(condition, value1, value2)' | [condition: true, value1: "a", value2: "b"]  || "a"
        'IF(condition, value1, value2)' | [condition: false, value1: "a", value2: "b"] || "b"
        'IF(x > 5, "big", "small")'     | [x: 10]                               || "big"
        'IF(x > 5, "big", "small")'     | [x: 3]                                || "small"
    }

    def "test ISNULL evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression          | bindings                              || expectedResult
        'ISNULL(null)'      | [:]                                   || true
        'ISNULL("test")'    | [:]                                   || false
        'ISNULL("")'        | [:]                                   || true
        'ISNULL([])'        | [:]                                   || true
        'ISNULL([1, 2])'    | [:]                                   || false
        'ISNULL(value)'     | [value: null]                         || true
        'ISNULL(value)'     | [value: "test"]                       || false
        'ISNULL(value)'     | [value: ""]                           || true
        'ISNULL(value)'     | [value: []]                           || true
        'ISNULL(value)'     | [value: [1, 2]]                       || false
    }

    def "test NOTNULL evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression          | bindings                              || expectedResult
        'NOTNULL(null)'     | [:]                                   || false
        'NOTNULL("test")'   | [:]                                   || true
        'NOTNULL("")'       | [:]                                   || false
        'NOTNULL([])'       | [:]                                   || false
        'NOTNULL([1, 2])'   | [:]                                   || true
        'NOTNULL(value)'    | [value: null]                         || false
        'NOTNULL(value)'    | [value: "test"]                       || true
        'NOTNULL(value)'    | [value: ""]                           || false
    }

    def "test ISBLANK and NOTBLANK evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression              | bindings                          || expectedResult
        'ISBLANK("")'           | [:]                               || true
        'ISBLANK("   ")'        | [:]                               || true
        'ISBLANK("test")'       | [:]                               || false
        'ISBLANK(null)'         | [:]                               || true
        'NOTBLANK("")'          | [:]                               || false
        'NOTBLANK("   ")'       | [:]                               || false
        'NOTBLANK("test")'      | [:]                               || true
        'NOTBLANK(null)'        | [:]                               || false
        'ISBLANK(value)'        | [value: ""]                       || true
        'NOTBLANK(value)'       | [value: "test"]                   || true
    }

    def "test ISNUMBER evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression              | bindings                          || expectedResult
        'ISNUMBER("123")'       | [:]                               || true
        'ISNUMBER("123.45")'    | [:]                               || true
        'ISNUMBER("-123")'      | [:]                               || true
        'ISNUMBER("+123")'      | [:]                               || true
        'ISNUMBER("123,456")'   | [:]                               || false
        'ISNUMBER("123.45E2")' | [:]                               || true
        'ISNUMBER("abc")'       | [:]                               || false
        'ISNUMBER("12a3")'      | [:]                               || false
        'ISNUMBER("")'          | [:]                               || false
        'ISNUMBER(value)'       | [value: "456"]                    || true
        'ISNUMBER(value)'       | [value: "abc"]                    || false
    }

    def "test NULLVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                          | bindings                          || expectedResult
        'NULLVALUE(null, "default")'        | [:]                               || "default"
        'NULLVALUE("", "default")'          | [:]                               || "default"
        'NULLVALUE("value", "default")'     | [:]                               || null
        'NULLVALUE([], "default")'          | [:]                               || "default"
        'NULLVALUE([1, 2], "default")'      | [:]                               || null
        'NULLVALUE(cond, defaultVal)'       | [cond: null, defaultVal: "def"]   || "def"
        'NULLVALUE(cond, defaultVal)'       | [cond: "val", defaultVal: "def"]  || null
    }

    def "test NULL2DEFAULT evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                              | bindings                              || expectedResult
        'NULL2DEFAULT(null, "default")'         | [:]                                   || "default"
        'NULL2DEFAULT("", "default")'           | [:]                                   || "default"
        'NULL2DEFAULT("value", "default")'      | [:]                                   || "value"
        'NULL2DEFAULT([], "default")'           | [:]                                   || "default"
        'NULL2DEFAULT([1, 2], "default")'       | [:]                                   || [1, 2]
        'NULL2DEFAULT(value, defaultVal)'       | [value: null, defaultVal: "def"]      || "def"
        'NULL2DEFAULT(value, defaultVal)'       | [value: "val", defaultVal: "def"]     || "val"
        'NULL2DEFAULT(value, defaultVal)'       | [value: "", defaultVal: "def"]        || "def"
    }

    def "test complex logic expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        'AND(NOT(ISNULL(name)), NOTBLANK(name))'               | [name: "John"]                                || true
        'AND(NOT(ISNULL(name)), NOTBLANK(name))'               | [name: ""]                                    || false
        'AND(NOT(ISNULL(name)), NOTBLANK(name))'               | [name: null]                                  || false
        'OR(ISNULL(value), ISNUMBER(value))'                   | [value: "123"]                                || true
        'OR(ISNULL(value), ISNUMBER(value))'                   | [value: "abc"]                                || false
        'OR(ISNULL(value), ISNUMBER(value))'                   | [value: null]                                 || true
        'IF(ISNUMBER(input), VALUE(input), 0)'                 | [input: "123"]                                || new BigDecimal("123")
        'IF(ISNUMBER(input), VALUE(input), 0)'                 | [input: "abc"]                                || 0
        'NULL2DEFAULT(IF(ISBLANK(name), null, name), "Unknown")' | [name: "John"]                              || "John"
        'NULL2DEFAULT(IF(ISBLANK(name), null, name), "Unknown")' | [name: ""]                                  || "Unknown"
    }
}
