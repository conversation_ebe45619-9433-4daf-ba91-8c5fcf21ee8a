package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import spock.lang.Specification

/**
 * LogicCaseFunction 单元测试
 * 测试 CASE 函数的各种重载版本的 compile 和 evaluate 场景
 */
class LogicCaseFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test CASE functions compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                // 1 condition
                'CASE(1, 1, "one", "default")',
                // 2 conditions
                'CASE(2, 1, "one", 2, "two", "default")',
                // 3 conditions
                'CASE(3, 1, "one", 2, "two", 3, "three", "default")',
                // 4 conditions
                'CASE(4, 1, "one", 2, "two", 3, "three", 4, "four", "default")',
                // 5 conditions
                'CASE(5, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", "default")',
                // 6 conditions
                'CASE(6, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", "default")',
                // 7 conditions
                'CASE(7, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", "default")',
                // 8 conditions
                'CASE(8, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", "default")',
                // 9 conditions
                'CASE(9, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", 9, "nine", "default")',
                // 10 conditions
                'CASE(10, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", 9, "nine", 10, "ten", "default")',
                // Different data types
                'CASE("A", "A", "Apple", "B", "Banana", "C", "Cherry", "Unknown")',
                'CASE("ACTIVE", "ACTIVE", "活跃", "INACTIVE", "非活跃", "未知")',
                'CASE(90, 90, "A", 80, "B", 70, "C", 60, "D", "F")',
                'CASE(true, true, "YES", false, "NO", "UNKNOWN")',
                'CASE(1.5, 1.5, "one_half", 2.5, "two_half", "other")',
                // Complex expressions
                'CASE(status, "ACTIVE", level + 1, "INACTIVE", level - 1, level)',
                'CASE(type, "A", value * 2, "B", value / 2, value)',
                // Nested CASE
                'CASE(outer, 1, CASE(inner, 1, "1-1", "1-other"), "outer-other")'
        ]
    }

    def "test CASE with 1 condition evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                              | bindings                          || expectedResult
        'CASE(1, 1, "one", "default")'          | [:]                               || "one"
        'CASE(2, 1, "one", "default")'          | [:]                               || "default"
        'CASE("A", "A", "Apple", "Unknown")'    | [:]                               || "Apple"
        'CASE("B", "A", "Apple", "Unknown")'    | [:]                               || "Unknown"
        'CASE(value, 1, "first", "other")'      | [value: 1]                        || "first"
        'CASE(value, 1, "first", "other")'      | [value: 2]                        || "other"
    }

    def "test CASE with 2 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                      | bindings                          || expectedResult
        'CASE(1, 1, "one", 2, "two", "default")'        | [:]                               || "one"
        'CASE(2, 1, "one", 2, "two", "default")'        | [:]                               || "two"
        'CASE(3, 1, "one", 2, "two", "default")'        | [:]                               || "default"
        'CASE("A", "A", "Apple", "B", "Banana", "Unknown")' | [:]                           || "Apple"
        'CASE("B", "A", "Apple", "B", "Banana", "Unknown")' | [:]                           || "Banana"
        'CASE("C", "A", "Apple", "B", "Banana", "Unknown")' | [:]                           || "Unknown"
        'CASE(status, "ACTIVE", 1, "INACTIVE", 0, -1)'  | [status: "ACTIVE"]                || 1
        'CASE(status, "ACTIVE", 1, "INACTIVE", 0, -1)'  | [status: "INACTIVE"]              || 0
        'CASE(status, "ACTIVE", 1, "INACTIVE", 0, -1)'  | [status: "PENDING"]               || -1
    }

    def "test CASE with 3 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                  | bindings                          || expectedResult
        'CASE(1, 1, "one", 2, "two", 3, "three", "default")'       | [:]                               || "one"
        'CASE(2, 1, "one", 2, "two", 3, "three", "default")'       | [:]                               || "two"
        'CASE(3, 1, "one", 2, "two", 3, "three", "default")'       | [:]                               || "three"
        'CASE(4, 1, "one", 2, "two", 3, "three", "default")'       | [:]                               || "default"
        'CASE(grade, "A", 90, "B", 80, "C", 70, 0)'                | [grade: "A"]                      || 90
        'CASE(grade, "A", 90, "B", 80, "C", 70, 0)'                | [grade: "B"]                      || 80
        'CASE(grade, "A", 90, "B", 80, "C", 70, 0)'                | [grade: "C"]                      || 70
        'CASE(grade, "A", 90, "B", 80, "C", 70, 0)'                | [grade: "F"]                      || 0
    }

    def "test CASE with 4 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                          | bindings                          || expectedResult
        'CASE(1, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "one"
        'CASE(2, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "two"
        'CASE(3, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "three"
        'CASE(4, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "four"
        'CASE(5, 1, "one", 2, "two", 3, "three", 4, "four", "default")'    | [:]                               || "default"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 1]                          || "Mon"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 2]                          || "Tue"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 3]                          || "Wed"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 4]                          || "Thu"
        'CASE(day, 1, "Mon", 2, "Tue", 3, "Wed", 4, "Thu", "Other")'       | [day: 5]                          || "Other"
    }

    def "test CASE with 5 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                      | bindings                          || expectedResult
        'CASE(1, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", "default")'     | [:]                               || "one"
        'CASE(5, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", "default")'     | [:]                               || "five"
        'CASE(6, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", "default")'     | [:]                               || "default"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 90]                       || "A"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 80]                       || "B"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 70]                       || "C"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 60]                       || "D"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 50]                       || "E"
        'CASE(score, 90, "A", 80, "B", 70, "C", 60, "D", 50, "E", "F")'                | [score: 40]                       || "F"
    }

    def "test CASE with different data types"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                  | bindings                              || expectedResult
        'CASE(type, "STRING", 1, "NUMBER", 2, "BOOLEAN", 3, 0)'    | [type: "STRING"]                      || 1
        'CASE(type, "STRING", 1, "NUMBER", 2, "BOOLEAN", 3, 0)'    | [type: "NUMBER"]                      || 2
        'CASE(type, "STRING", 1, "NUMBER", 2, "BOOLEAN", 3, 0)'    | [type: "BOOLEAN"]                     || 3
        'CASE(type, "STRING", 1, "NUMBER", 2, "BOOLEAN", 3, 0)'    | [type: "OTHER"]                       || 0
        'CASE(flag, true, "YES", false, "NO", "UNKNOWN")'          | [flag: true]                          || "YES"
        'CASE(flag, true, "YES", false, "NO", "UNKNOWN")'          | [flag: false]                         || "NO"
        'CASE(num, 1.0, "one", 2.0, "two", 3.0, "three", "other")' | [num: 1.0]                           || "one"
        'CASE(num, 1.0, "one", 2.0, "two", 3.0, "three", "other")' | [num: 2.0]                           || "two"
        'CASE(num, 1.0, "one", 2.0, "two", 3.0, "three", "other")' | [num: 4.0]                           || "other"
    }

    def "test CASE with null values"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        thrown(expectedException)
        where:
        expression                                          | bindings                          || expectedException
        'CASE(null, null, "null_value", "not_null")'       | [:]                               || NullPointerException
        'CASE(null, "test", "test_value", "default")'      | [:]                               || NullPointerException
        'CASE(value, null, "is_null", "not_null")'         | [value: null]                     || NullPointerException
    }

    def "test CASE with valid null comparisons"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                          | bindings                          || expectedResult
        'CASE("test", null, "null_value", "default")'      | [:]                               || "default"
        'CASE("test", "test", "match", "default")'         | [:]                               || "match"
        'CASE(value, "test", "is_test", "not_test")'       | [value: "test"]                   || "is_test"
        'CASE(value, "test", "is_test", "not_test")'       | [value: "other"]                  || "not_test"
    }

    def "test CASE with 6-10 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                                              | bindings                          || expectedResult
        // 6 conditions
        'CASE(6, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", "default")'                  | [:]                               || "six"
        'CASE(7, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", "default")'                  | [:]                               || "default"
        // 7 conditions
        'CASE(7, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", "default")'      | [:]                               || "seven"
        'CASE(8, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", "default")'      | [:]                               || "default"
        // 8 conditions
        'CASE(8, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", "default")' | [:] || "eight"
        'CASE(9, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", "default")' | [:] || "default"
        // 9 conditions
        'CASE(9, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", 9, "nine", "default")' | [:] || "nine"
        'CASE(10, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", 9, "nine", "default")' | [:] || "default"
        // 10 conditions
        'CASE(10, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", 9, "nine", 10, "ten", "default")' | [:] || "ten"
        'CASE(11, 1, "one", 2, "two", 3, "three", 4, "four", 5, "five", 6, "six", 7, "seven", 8, "eight", 9, "nine", 10, "ten", "default")' | [:] || "default"
    }

    def "test CASE with 11-20 conditions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                                              | bindings                          || expectedResult
        // 15 conditions test
        'CASE(month, 1, "Jan", 2, "Feb", 3, "Mar", 4, "Apr", 5, "May", 6, "Jun", 7, "Jul", 8, "Aug", 9, "Sep", 10, "Oct", 11, "Nov", 12, "Dec", 13, "Q1", 14, "Q2", 15, "Q3", "Unknown")' | [month: 1] || "Jan"
        'CASE(month, 1, "Jan", 2, "Feb", 3, "Mar", 4, "Apr", 5, "May", 6, "Jun", 7, "Jul", 8, "Aug", 9, "Sep", 10, "Oct", 11, "Nov", 12, "Dec", 13, "Q1", 14, "Q2", 15, "Q3", "Unknown")' | [month: 12] || "Dec"
        'CASE(month, 1, "Jan", 2, "Feb", 3, "Mar", 4, "Apr", 5, "May", 6, "Jun", 7, "Jul", 8, "Aug", 9, "Sep", 10, "Oct", 11, "Nov", 12, "Dec", 13, "Q1", 14, "Q2", 15, "Q3", "Unknown")' | [month: 15] || "Q3"
        'CASE(month, 1, "Jan", 2, "Feb", 3, "Mar", 4, "Apr", 5, "May", 6, "Jun", 7, "Jul", 8, "Aug", 9, "Sep", 10, "Oct", 11, "Nov", 12, "Dec", 13, "Q1", 14, "Q2", 15, "Q3", "Unknown")' | [month: 16] || "Unknown"
        // 20 conditions test
        'CASE(code, 1, "A1", 2, "A2", 3, "A3", 4, "A4", 5, "A5", 6, "B1", 7, "B2", 8, "B3", 9, "B4", 10, "B5", 11, "C1", 12, "C2", 13, "C3", 14, "C4", 15, "C5", 16, "D1", 17, "D2", 18, "D3", 19, "D4", 20, "D5", "UNKNOWN")' | [code: 1] || "A1"
        'CASE(code, 1, "A1", 2, "A2", 3, "A3", 4, "A4", 5, "A5", 6, "B1", 7, "B2", 8, "B3", 9, "B4", 10, "B5", 11, "C1", 12, "C2", 13, "C3", 14, "C4", 15, "C5", 16, "D1", 17, "D2", 18, "D3", 19, "D4", 20, "D5", "UNKNOWN")' | [code: 10] || "B5"
        'CASE(code, 1, "A1", 2, "A2", 3, "A3", 4, "A4", 5, "A5", 6, "B1", 7, "B2", 8, "B3", 9, "B4", 10, "B5", 11, "C1", 12, "C2", 13, "C3", 14, "C4", 15, "C5", 16, "D1", 17, "D2", 18, "D3", 19, "D4", 20, "D5", "UNKNOWN")' | [code: 20] || "D5"
        'CASE(code, 1, "A1", 2, "A2", 3, "A3", 4, "A4", 5, "A5", 6, "B1", 7, "B2", 8, "B3", 9, "B4", 10, "B5", 11, "C1", 12, "C2", 13, "C3", 14, "C4", 15, "C5", 16, "D1", 17, "D2", 18, "D3", 19, "D4", 20, "D5", "UNKNOWN")' | [code: 21] || "UNKNOWN"
    }

    def "test CASE with large number of conditions (30-50)"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                                              | bindings                          || expectedResult
        // 30 conditions test - testing performance and correctness
        'CASE(value, 1, "V1", 2, "V2", 3, "V3", 4, "V4", 5, "V5", 6, "V6", 7, "V7", 8, "V8", 9, "V9", 10, "V10", 11, "V11", 12, "V12", 13, "V13", 14, "V14", 15, "V15", 16, "V16", 17, "V17", 18, "V18", 19, "V19", 20, "V20", 21, "V21", 22, "V22", 23, "V23", 24, "V24", 25, "V25", 26, "V26", 27, "V27", 28, "V28", 29, "V29", 30, "V30", "DEFAULT")' | [value: 1] || "V1"
        'CASE(value, 1, "V1", 2, "V2", 3, "V3", 4, "V4", 5, "V5", 6, "V6", 7, "V7", 8, "V8", 9, "V9", 10, "V10", 11, "V11", 12, "V12", 13, "V13", 14, "V14", 15, "V15", 16, "V16", 17, "V17", 18, "V18", 19, "V19", 20, "V20", 21, "V21", 22, "V22", 23, "V23", 24, "V24", 25, "V25", 26, "V26", 27, "V27", 28, "V28", 29, "V29", 30, "V30", "DEFAULT")' | [value: 15] || "V15"
        'CASE(value, 1, "V1", 2, "V2", 3, "V3", 4, "V4", 5, "V5", 6, "V6", 7, "V7", 8, "V8", 9, "V9", 10, "V10", 11, "V11", 12, "V12", 13, "V13", 14, "V14", 15, "V15", 16, "V16", 17, "V17", 18, "V18", 19, "V19", 20, "V20", 21, "V21", 22, "V22", 23, "V23", 24, "V24", 25, "V25", 26, "V26", 27, "V27", 28, "V28", 29, "V29", 30, "V30", "DEFAULT")' | [value: 30] || "V30"
        'CASE(value, 1, "V1", 2, "V2", 3, "V3", 4, "V4", 5, "V5", 6, "V6", 7, "V7", 8, "V8", 9, "V9", 10, "V10", 11, "V11", 12, "V12", 13, "V13", 14, "V14", 15, "V15", 16, "V16", 17, "V17", 18, "V18", 19, "V19", 20, "V20", 21, "V21", 22, "V22", 23, "V23", 24, "V24", 25, "V25", 26, "V26", 27, "V27", 28, "V28", 29, "V29", 30, "V30", "DEFAULT")' | [value: 31] || "DEFAULT"
    }

    def "test CASE with mixed data types and edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                      | bindings                              || expectedResult
        // Mixed numeric types
        'CASE(value, 1, "int", 1.0, "double", 1.5, "decimal", "other")'               | [value: 1]                            || "int"
        'CASE(value, 1, "int", 1.0, "double", 1.5, "decimal", "other")'               | [value: 1.0]                          || "double"
        'CASE(value, 1, "int", 1.0, "double", 1.5, "decimal", "other")'               | [value: 1.5]                          || "decimal"
        'CASE(value, 1, "int", 1.0, "double", 1.5, "decimal", "other")'               | [value: 2]                            || "other"
        // Boolean values
        'CASE(flag, true, "TRUE", false, "FALSE", "NULL")'                            | [flag: true]                          || "TRUE"
        'CASE(flag, true, "TRUE", false, "FALSE", "NULL")'                            | [flag: false]                         || "FALSE"
        'CASE(flag, true, "TRUE", false, "FALSE", "NULL")'                            | [flag: null]                          || "NULL"
        // String cases with special characters
        'CASE(text, "", "EMPTY", " ", "SPACE", "null", "NULL_STR", "other")'          | [text: ""]                            || "EMPTY"
        'CASE(text, "", "EMPTY", " ", "SPACE", "null", "NULL_STR", "other")'          | [text: " "]                           || "SPACE"
        'CASE(text, "", "EMPTY", " ", "SPACE", "null", "NULL_STR", "other")'          | [text: "null"]                        || "NULL_STR"
        'CASE(text, "", "EMPTY", " ", "SPACE", "null", "NULL_STR", "other")'          | [text: "test"]                        || "other"
        // Unicode and special characters
        'CASE(lang, "中文", "Chinese", "English", "英文", "Français", "French", "Unknown")' | [lang: "中文"]                        || "Chinese"
        'CASE(lang, "中文", "Chinese", "English", "英文", "Français", "French", "Unknown")' | [lang: "English"]                     || "英文"
        'CASE(lang, "中文", "Chinese", "English", "英文", "Français", "French", "Unknown")' | [lang: "Français"]                    || "French"
        'CASE(lang, "中文", "Chinese", "English", "英文", "Français", "French", "Unknown")' | [lang: "Deutsch"]                     || "Unknown"
    }

    def "test CASE performance and stress scenarios"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                      | bindings                              || expectedResult
        // Large string values
        'CASE(type, "VERY_LONG_STRING_VALUE_FOR_TESTING_PERFORMANCE", "MATCH", "SHORT", "SHORT_MATCH", "DEFAULT")' | [type: "VERY_LONG_STRING_VALUE_FOR_TESTING_PERFORMANCE"] || "MATCH"
        'CASE(type, "VERY_LONG_STRING_VALUE_FOR_TESTING_PERFORMANCE", "MATCH", "SHORT", "SHORT_MATCH", "DEFAULT")' | [type: "SHORT"] || "SHORT_MATCH"
        'CASE(type, "VERY_LONG_STRING_VALUE_FOR_TESTING_PERFORMANCE", "MATCH", "SHORT", "SHORT_MATCH", "DEFAULT")' | [type: "OTHER"] || "DEFAULT"
        // Numeric edge cases
        'CASE(num, 0, "ZERO", -1, "NEGATIVE", 999999999, "LARGE", "OTHER")'           | [num: 0]                              || "ZERO"
        'CASE(num, 0, "ZERO", -1, "NEGATIVE", 999999999, "LARGE", "OTHER")'           | [num: -1]                             || "NEGATIVE"
        'CASE(num, 0, "ZERO", -1, "NEGATIVE", 999999999, "LARGE", "OTHER")'           | [num: 999999999]                      || "LARGE"
        'CASE(num, 0, "ZERO", -1, "NEGATIVE", 999999999, "LARGE", "OTHER")'           | [num: 123]                            || "OTHER"
    }

    def "test complex CASE expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                      | bindings                                      || expectedResult
        'CASE(status, "ACTIVE", CASE(type, "VIP", "VIP_ACTIVE", "NORMAL_ACTIVE"), "INACTIVE")' | [status: "ACTIVE", type: "VIP"]       || "VIP_ACTIVE"
        'CASE(status, "ACTIVE", CASE(type, "VIP", "VIP_ACTIVE", "NORMAL_ACTIVE"), "INACTIVE")' | [status: "ACTIVE", type: "NORMAL"]    || "NORMAL_ACTIVE"
        'CASE(status, "ACTIVE", CASE(type, "VIP", "VIP_ACTIVE", "NORMAL_ACTIVE"), "INACTIVE")' | [status: "INACTIVE", type: "VIP"]     || "INACTIVE"
        'CASE(level, 1, "Level " + "1", 2, "Level " + "2", "Unknown")'                         | [level: 1]                            || "Level 1"
        'CASE(level, 1, "Level " + "1", 2, "Level " + "2", "Unknown")'                         | [level: 2]                            || "Level 2"
        'CASE(level, 1, "Level " + "1", 2, "Level " + "2", "Unknown")'                         | [level: 3]                            || "Unknown"
        // Arithmetic in CASE results
        'CASE(op, "ADD", a + b, "SUB", a - b, "MUL", a * b, "DIV", a / b, 0)'                  | [op: "ADD", a: 10, b: 5]              || 15
        'CASE(op, "ADD", a + b, "SUB", a - b, "MUL", a * b, "DIV", a / b, 0)'                  | [op: "SUB", a: 10, b: 5]              || 5
        'CASE(op, "ADD", a + b, "SUB", a - b, "MUL", a * b, "DIV", a / b, 0)'                  | [op: "MUL", a: 10, b: 5]              || 50
        'CASE(op, "ADD", a + b, "SUB", a - b, "MUL", a * b, "DIV", a / b, 0)'                  | [op: "DIV", a: 10, b: 5]              || 2
        'CASE(op, "ADD", a + b, "SUB", a - b, "MUL", a * b, "DIV", a / b, 0)'                  | [op: "OTHER", a: 10, b: 5]            || 0
    }


}
