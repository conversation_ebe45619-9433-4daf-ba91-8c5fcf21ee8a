package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import com.facishare.paas.expression.type.*
import spock.lang.Specification

/**
 * DateTimeFunction 单元测试
 * 测试 NOW, DATETIMEVALUE, LONG<PERSON>TETIMEVALUE, YEARS, MONTHS, DAYS, HOURS, MINUTES, DATEVALUE, LONGDATEVALUE, DATE, TODAY, YEAR, MONTH, DAY, DATETODATETIME, DATETIMETODATE, DATETIMETOTIME, TOTIMESTAMP 等方法的 compile 和 evaluate 场景
 */
class DateTimeFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test datetime functions compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'NOW()',
                'DATETIMEVALUE("2023-01-01 12:00:00")',
                'LONGDATETIMEVALUE(1672531200000L)',
                'YEARS(5)',
                'MONTHS(3)',
                'DAYS(10)',
                'HOURS(2)',
                'MINUTES(30)',
                'DATEVALUE("2023-01-01")',
                'LONGDATEVALUE(1672531200000L)',
                'DATE(2023, 1, 1)',
                'DATE()',
                'TODAY()',
                'YEAR(DATEVALUE("2023-01-01"))',
                'MONTH(DATEVALUE("2023-01-01"))',
                'DAY(DATEVALUE("2023-01-01"))',
                'DATETODATETIME(DATEVALUE("2023-01-01"))',
                'DATETIMETODATE(DATETIMEVALUE("2023-01-01 12:00:00"))',
                'DATETIMETOTIME(DATETIMEVALUE("2023-01-01 12:00:00"))',
                'TOTIMESTAMP(DATEVALUE("2023-01-01"))',
                'TOTIMESTAMP(DATETIMEVALUE("2023-01-01 12:00:00"))'
        ]
    }

    def "test NOW evaluate"() {
        when:
        def result = engine.evaluate('NOW()', [:])
        then:
        result instanceof PDateTime
        // NOW() should return current time, so we just check it's recent
        Math.abs(result.toTimeStamp() - System.currentTimeMillis()) < 5000
    }

    def "test DATETIMEVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                  | bindings                                      || expectedResult
        'DATETIMEVALUE("2023-01-01 12:00:00")'      | [:]                                           || PDateTime.of("2023-01-01 12:00:00")
        'DATETIMEVALUE("2023-12-31 23:59:59")'      | [:]                                           || PDateTime.of("2023-12-31 23:59:59")
        'DATETIMEVALUE(dateStr)'                    | [dateStr: "2023-06-15 10:30:45"]             || PDateTime.of("2023-06-15 10:30:45")
    }

    def "test LONGDATETIMEVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result instanceof PDateTime
        result.toTimeStamp() > 0
        where:
        expression                          | bindings
        'LONGDATETIMEVALUE(1672531200000L)' | [:]
        'LONGDATETIMEVALUE(timestamp)'      | [timestamp: 1672531200000L]
    }

    def "test time unit constructors evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result.getValue() == expectedValue
        where:
        expression          | bindings              || expectedValue
        'YEARS(5)'          | [:]                   || 5
        'YEARS(value)'      | [value: 3]            || 3
        'MONTHS(12)'        | [:]                   || 12
        'MONTHS(value)'     | [value: 6]            || 6
        'DAYS(30)'          | [:]                   || 30
        'DAYS(value)'       | [value: 15]           || 15
        'HOURS(24)'         | [:]                   || 24
        'HOURS(value)'      | [value: 12]           || 12
        'MINUTES(60)'       | [:]                   || 60
        'MINUTES(value)'    | [value: 30]           || 30
    }

    def "test DATEVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                          || expectedResult
        'DATEVALUE("2023-01-01")'       | [:]                               || PDate.of("2023-01-01")
        'DATEVALUE("2023-12-31")'       | [:]                               || PDate.of("2023-12-31")
        'DATEVALUE(dateStr)'            | [dateStr: "2023-06-15"]           || PDate.of("2023-06-15")
    }

    def "test LONGDATEVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result instanceof PDate
        result.toTimeStamp() > 0
        where:
        expression                      | bindings
        'LONGDATEVALUE(1672531200000L)' | [:]
        'LONGDATEVALUE(timestamp)'      | [timestamp: 1672531200000L]
    }

    def "test DATE constructor evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                                      || expectedResult
        'DATE(2023, 1, 1)'              | [:]                                           || PDate.of(2023, 1, 1)
        'DATE(2023, 12, 31)'            | [:]                                           || PDate.of(2023, 12, 31)
        'DATE(year, month, day)'        | [year: 2023, month: 6, day: 15]               || PDate.of(2023, 6, 15)
    }

    def "test DATE and TODAY evaluate"() {
        when:
        def dateResult = engine.evaluate('DATE()', [:])
        def todayResult = engine.evaluate('TODAY()', [:])
        then:
        dateResult instanceof PDate
        todayResult instanceof PDate
        // Both should return today's date
        dateResult.getYear() == todayResult.getYear()
        dateResult.getMonth() == todayResult.getMonth()
        dateResult.getDay() == todayResult.getDay()
    }

    def "test YEAR, MONTH, DAY evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                      | bindings                                      || expectedResult
        'YEAR(DATEVALUE("2023-06-15"))'                 | [:]                                           || 2023
        'MONTH(DATEVALUE("2023-06-15"))'                | [:]                                           || 6
        'DAY(DATEVALUE("2023-06-15"))'                  | [:]                                           || 15
        'YEAR(DATETIMEVALUE("2023-06-15 10:30:45"))'    | [:]                                           || 2023
        'MONTH(DATETIMEVALUE("2023-06-15 10:30:45"))'   | [:]                                           || 6
        'DAY(DATETIMEVALUE("2023-06-15 10:30:45"))'     | [:]                                           || 15
        'YEAR(date)'                                    | [date: PDate.of("2023-12-31")]               || 2023
        'MONTH(date)'                                   | [date: PDate.of("2023-12-31")]               || 12
        'DAY(date)'                                     | [date: PDate.of("2023-12-31")]               || 31
    }

    def "test date conversion functions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        'DATETODATETIME(DATEVALUE("2023-06-15"))'               | [:]                                           || PDateTime.of(2023, 6, 15, 0, 0, 0)
        'DATETIMETODATE(DATETIMEVALUE("2023-06-15 10:30:45"))'  | [:]                                           || PDate.of(2023, 6, 15)
        'DATETIMETOTIME(DATETIMEVALUE("2023-06-15 10:30:45"))'  | [:]                                           || PTime.of(PDateTime.of("2023-06-15 10:30:45").toTimeStamp())
    }

    def "test TOTIMESTAMP evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result instanceof Long
        result > 0
        where:
        expression                                      | bindings
        'TOTIMESTAMP(DATEVALUE("2023-01-01"))'          | [:]
        'TOTIMESTAMP(DATETIMEVALUE("2023-01-01 12:00:00"))' | [:]
        'TOTIMESTAMP(DATETIMETOTIME(DATETIMEVALUE("2023-01-01 12:00:00")))' | [:]
    }

    def "test complex datetime expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                      | bindings                                      || expectedResult
        'YEAR(DATETIMETODATE(DATETIMEVALUE("2023-06-15 10:30:45")))'    | [:]                                           || 2023
        'MONTH(DATETODATETIME(DATEVALUE("2023-06-15")))'                | [:]                                           || 6
        'DAY(DATEVALUE("2023-06-15"))'                                  | [:]                                           || 15
        'YEAR(DATETIMETODATE(DATETIMEVALUE("2023-12-31 23:59:59")))'    | [:]                                           || 2023
    }

    def "test datetime edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        'YEAR(DATEVALUE("2023-12-31"))'                        | [:]                                           || 2023
        'MONTH(DATEVALUE("2023-01-01"))'                       | [:]                                           || 1
        'DAY(DATEVALUE("2023-12-31"))'                         | [:]                                           || 31
        'YEAR(DATETIMEVALUE("2023-06-15 23:59:59"))'           | [:]                                           || 2023
        'MONTH(DATETIMEVALUE("2023-06-15 00:00:00"))'          | [:]                                           || 6
        'DAY(DATETIMEVALUE("2023-06-15 12:30:45"))'            | [:]                                           || 15
    }
}
